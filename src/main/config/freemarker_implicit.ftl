[#ftl]
 @implicitly included
 @ftlvariable name="initializeMemorySize" type="java.lang.Number"
 @ftlvariable name="currentUrl" type="java.lang.String"
 @ftlvariable name="file" type="cn.keking.model.FileAttribute"
 @ftlvariable name="fileName" type="java.lang.String"
 @ftlvariable name="fileTree" type="java.lang.String"
 @ftlvariable name="baseUrl" type="java.lang.String"
 @ftlvariable name="imgUrls" type="String"
 @ftlvariable name="textData" type="java.lang.String"
 @ftlvariable name="xmlContent" type="java.lang.String"
 @ftlvariable name="textContent" type="java.lang.String"
 @ftlvariable name="textType" type="java.lang.String"
 @ftlvariable name="markdown" type="String"
 @ftlvariable name="xml" type="String"
 @ftlvariable name="switchDisabled" type="String"
 @ftlvariable name="imgurls" type="String"
 @ftlvariable name="watermarkAngle" type="String"
 @ftlvariable name="watermarkHeight" type="String"
 @ftlvariable name="watermarkWidth" type="String"
 @ftlvariable name="watermarkAlpha" type="String"
 @ftlvariable name="watermarkColor" type="String"
 @ftlvariable name="watermarkFontsize" type="String"
 @ftlvariable name="watermarkFont" type="String"
 @ftlvariable name="watermarkYSpace" type="String"
 @ftlvariable name="watermarkXSpace" type="String"
 @ftlvariable name="watermarkTxt" type="String"
 @ftlvariable name="ordinaryUrl" type="String"
