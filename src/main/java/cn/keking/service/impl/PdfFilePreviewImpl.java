package cn.keking.service.impl;

import cn.keking.config.ConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import cn.keking.service.FilePreview;
import cn.keking.utils.DownloadUtils;
import cn.keking.service.FileHandlerService;
import cn.keking.utils.StringPathUtils;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import static cn.keking.service.impl.OfficeFilePreviewImpl.FILE_DIR;
import static cn.keking.service.impl.OfficeFilePreviewImpl.addWaterMark;

/**
 * Created by kl on 2018/1/17.
 * Content :处理pdf文件
 */
@Service
public class PdfFilePreviewImpl implements FilePreview {

    private final FileHandlerService fileHandlerService;
    private final OtherFilePreviewImpl otherFilePreview;

    public PdfFilePreviewImpl(FileHandlerService fileHandlerService, OtherFilePreviewImpl otherFilePreview) {
        this.fileHandlerService = fileHandlerService;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        String fileName = fileAttribute.getName();

        // 对文件名进行URL编码处理，确保保存和访问时使用相同的文件名格式
        String encodedFileName;
        try {
            encodedFileName = encodeFileName(fileName);
        } catch (Exception e) {
            encodedFileName = fileName;
        }

        String pdfName = encodedFileName.substring(0, encodedFileName.lastIndexOf(".") + 1) + "pdf";
        String cacheFileName = encodedFileName.substring(0, encodedFileName.lastIndexOf(".")) + "_wm.pdf";

        //当文件不存在时，就去下载
        if (!fileHandlerService.listConvertedFiles().containsKey(cacheFileName) || !ConfigConstants.isCacheEnabled()) {
            ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, fileName);
            if (response.isFailure()) {
                return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
            }
            String outFilePath = response.getContent();
            pdfName = encodedFileName.substring(0, encodedFileName.lastIndexOf(".")) + "_wm.pdf";
            String wateredOutFilePath = FILE_DIR + pdfName;
            try {
                addWaterMark(outFilePath, wateredOutFilePath, fileAttribute.getWatermark());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ConfigConstants.isCacheEnabled()) {
                // 加入缓存
                fileHandlerService.addConvertedFile(cacheFileName, fileHandlerService.getRelativePath(wateredOutFilePath));
            }
        }
        model.addAttribute("pdfUrl", StringPathUtils.pathUtil(cacheFileName));
        return PDF_FILE_PREVIEW_PAGE;

    }

    /**
     * 对文件名进行URL编码，确保文件名中的中文字符和特殊字符能正确处理
     * @param fileName 原始文件名
     * @return 编码后的文件名
     * @throws UnsupportedEncodingException 编码异常
     */
    private String encodeFileName(String fileName) throws UnsupportedEncodingException {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        // 分离文件名和扩展名
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // 没有扩展名，直接编码整个文件名
            return URLEncoder.encode(fileName, "UTF-8")
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\%21", "!")
                    .replaceAll("\\%27", "'")
                    .replaceAll("\\%28", "(")
                    .replaceAll("\\%29", ")")
                    .replaceAll("\\%7E", "~");
        } else {
            // 有扩展名，只编码文件名部分，保留扩展名
            String nameWithoutExt = fileName.substring(0, lastDotIndex);
            String extension = fileName.substring(lastDotIndex);

            String encodedName = URLEncoder.encode(nameWithoutExt, "UTF-8")
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\%21", "!")
                    .replaceAll("\\%27", "'")
                    .replaceAll("\\%28", "(")
                    .replaceAll("\\%29", ")")
                    .replaceAll("\\%7E", "~");

            return encodedName + extension;
        }
    }
}
