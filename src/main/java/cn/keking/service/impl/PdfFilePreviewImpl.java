package cn.keking.service.impl;

import cn.keking.config.ConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import cn.keking.service.FilePreview;
import cn.keking.utils.DownloadUtils;
import cn.keking.service.FileHandlerService;
import cn.keking.utils.StringPathUtils;
import cn.keking.web.filter.BaseUrlFilter;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.ArrayList;
import java.util.List;

import static cn.keking.service.impl.OfficeFilePreviewImpl.FILE_DIR;
import static cn.keking.service.impl.OfficeFilePreviewImpl.addWaterMark;

/**
 * Created by kl on 2018/1/17.
 * Content :处理pdf文件
 */
@Service
public class PdfFilePreviewImpl implements FilePreview {

    private final FileHandlerService fileHandlerService;
    private final OtherFilePreviewImpl otherFilePreview;

    public PdfFilePreviewImpl(FileHandlerService fileHandlerService, OtherFilePreviewImpl otherFilePreview) {
        this.fileHandlerService = fileHandlerService;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        String fileName = fileAttribute.getName();
        String pdfName = fileName.substring(0, fileName.lastIndexOf(".") + 1) + "pdf";
        //当文件不存在时，就去下载
        if (!fileHandlerService.listConvertedFiles().containsKey(pdfName) || !ConfigConstants.isCacheEnabled()) {
            ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, fileName);
            if (response.isFailure()) {
                return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
            }
            String outFilePath = response.getContent();
            pdfName = fileName.substring(0, fileName.lastIndexOf(".")) + "_wm.pdf";
            String wateredOutFilePath = FILE_DIR + pdfName;
            try {
                addWaterMark(outFilePath, wateredOutFilePath, fileAttribute.getWatermark());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ConfigConstants.isCacheEnabled()) {
                // 加入缓存
                fileHandlerService.addConvertedFile(pdfName, fileHandlerService.getRelativePath(outFilePath));
            }
        }
        model.addAttribute("pdfUrl", StringPathUtils.pathUtil(pdfName));
        return PDF_FILE_PREVIEW_PAGE;

    }
}
