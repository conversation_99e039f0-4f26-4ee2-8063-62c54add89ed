package cn.keking.service.impl;

import cn.keking.config.ConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import cn.keking.service.FilePreview;
import cn.keking.utils.DownloadUtils;
import cn.keking.service.FileHandlerService;
import cn.keking.utils.StringPathUtils;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import static cn.keking.service.impl.OfficeFilePreviewImpl.FILE_DIR;
import static cn.keking.service.impl.OfficeFilePreviewImpl.addWaterMark;

/**
 * Created by kl on 2018/1/17.
 * Content :处理pdf文件
 */
@Service
public class PdfFilePreviewImpl implements FilePreview {

    private final FileHandlerService fileHandlerService;
    private final OtherFilePreviewImpl otherFilePreview;

    public PdfFilePreviewImpl(FileHandlerService fileHandlerService, OtherFilePreviewImpl otherFilePreview) {
        this.fileHandlerService = fileHandlerService;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        String fileName = fileAttribute.getName();

        // 对文件名进行URL编码处理，确保保存和访问时使用相同的文件名格式
        String encodedFileName;
        try {
            encodedFileName = encodeFileName(fileName);
        } catch (Exception e) {
            encodedFileName = fileName;
        }

        String pdfName = encodedFileName.substring(0, encodedFileName.lastIndexOf(".") + 1) + "pdf";
        String cacheFileName = encodedFileName.substring(0, encodedFileName.lastIndexOf(".")) + "_wm.pdf";

        //当文件不存在时，就去下载
        if (!fileHandlerService.listConvertedFiles().containsKey(cacheFileName) || !ConfigConstants.isCacheEnabled()) {
            ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, fileName);
            if (response.isFailure()) {
                return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
            }
            String outFilePath = response.getContent();
            pdfName = encodedFileName.substring(0, encodedFileName.lastIndexOf(".")) + "_wm.pdf";
            String wateredOutFilePath = FILE_DIR + pdfName;
            try {
                addWaterMark(outFilePath, wateredOutFilePath, fileAttribute.getWatermark());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ConfigConstants.isCacheEnabled()) {
                // 加入缓存
                fileHandlerService.addConvertedFile(cacheFileName, fileHandlerService.getRelativePath(wateredOutFilePath));
            }
        }
        model.addAttribute("pdfUrl", StringPathUtils.pathUtil(cacheFileName));
        return PDF_FILE_PREVIEW_PAGE;

    }

    /**
     * 对文件名进行安全处理，确保文件名中的中文字符和特殊字符能正确处理
     * 使用MD5哈希来避免文件名过长的问题
     * @param fileName 原始文件名
     * @return 处理后的文件名
     */
    private String encodeFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        // 分离文件名和扩展名
        int lastDotIndex = fileName.lastIndexOf('.');
        String nameWithoutExt = lastDotIndex == -1 ? fileName : fileName.substring(0, lastDotIndex);
        String extension = lastDotIndex == -1 ? "" : fileName.substring(lastDotIndex);

        // 检查文件名是否包含非ASCII字符或特殊字符
        boolean needsEncoding = !nameWithoutExt.matches("^[a-zA-Z0-9._-]+$");

        if (!needsEncoding) {
            // 如果文件名只包含安全字符，直接返回
            return fileName;
        }

        // 如果文件名包含特殊字符，使用MD5哈希 + 原始前缀的方式
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(nameWithoutExt.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            // 保留原文件名的前20个字符（如果是ASCII字符）作为可读性
            String prefix = "";
            for (int i = 0; i < Math.min(20, nameWithoutExt.length()); i++) {
                char c = nameWithoutExt.charAt(i);
                if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') ||
                    (c >= '0' && c <= '9') || c == '-' || c == '_') {
                    prefix += c;
                } else {
                    break;
                }
            }

            // 如果有可用的前缀，使用前缀_哈希的格式，否则只使用哈希
            String result = prefix.isEmpty() ? sb.toString() : prefix + "_" + sb.toString().substring(0, 16);
            return result + extension;

        } catch (Exception e) {
            // 如果哈希失败，使用时间戳作为备选方案
            return "file_" + System.currentTimeMillis() + extension;
        }
    }
}
