package cn.keking.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.keking.config.ConfigConstants;
import cn.keking.config.WatermarkConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import cn.keking.service.FileHandlerService;
import cn.keking.service.FilePreview;
import cn.keking.service.OfficeToPdfService;
import cn.keking.utils.DownloadUtils;
import cn.keking.utils.StringPathUtils;
import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.PdfSaveOptions;
import com.itextpdf.text.Element;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.hslf.HSLFSlideShow;
import org.apache.poi.hslf.usermodel.SlideShow;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xslf.XSLFSlideShow;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.artofsolving.jodconverter.OfficeDocumentConverter;
import org.artofsolving.jodconverter.util.XWFileOprUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by kl on 2018/1/17.
 * Content :处理office文件
 */
@Service
public class OfficeFilePreviewImpl implements FilePreview {

    private final Logger logger = LoggerFactory.getLogger(OfficeFilePreviewImpl.class);

    @Value("${fonts.path:/opt/libreoffice7.1/share/fonts/truetype}")
    private String fountPath;
    @Value("${office.maxPage:50}")
    private int maxPages;

    /**
     * 当前正在执行转换的文件
     * key 文件名
     * value 开始时间
     */
    private static Map<String, Long> inProcess = new ConcurrentHashMap<>(16);

    public static final String FILE_DIR = ConfigConstants.getFileDir();

    private final FileHandlerService fileHandlerService;
    private final OfficeToPdfService officeToPdfService;
    private final OtherFilePreviewImpl otherFilePreview;

    public OfficeFilePreviewImpl(FileHandlerService fileHandlerService, OfficeToPdfService officeToPdfService, OtherFilePreviewImpl otherFilePreview) {
        this.fileHandlerService = fileHandlerService;
        this.officeToPdfService = officeToPdfService;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        String suffix = fileAttribute.getSuffix();
        String fileName = fileAttribute.getName();
//        1684289507372_小米--承诺函通用版本.docx
        boolean isHtml = suffix.equalsIgnoreCase("xls") || suffix.equalsIgnoreCase("xlsx");
        String pdfName = fileName.substring(0, fileName.lastIndexOf(".") + 1) + (isHtml ? "html" : "pdf");
        String cacheFileName = fileName.substring(0, fileName.lastIndexOf(".")) + (isHtml ? ".html" : "_wm.pdf");
        String outFilePath = FILE_DIR + pdfName;
        // 判断之前是否已转换过，如果转换过，直接返回，否则执行转换
        if (!fileHandlerService.listConvertedFiles().containsKey(cacheFileName) || !ConfigConstants.isCacheEnabled()) {
            try {
                //上次执行的时间，如果不为空，说明还在处理，就直接返回；如果为空，才执行
                Long lastTime = inProcess.put(fileName, System.currentTimeMillis());
                if (null != lastTime) {
                    return otherFilePreview.notSupportedFile(model, fileAttribute, "当前文档正在处理，请稍后再试");
                }
                String filePath;
                logger.info("start download office " + DateUtil.now());
                ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, null);
                logger.info("end download office " + DateUtil.now());
                if (response.isFailure()) {
                    return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
                }
                filePath = response.getContent();
                /** switch (suffix) {
                    case "ppt":
                        //if (getPptPages(filePath) > maxPages) {
                            return otherFilePreview.notSupportedFile(model, fileAttribute, "页数大于" + maxPages + "页的文件请下载查看");
                       // }
                       // break;
                    case "pptx":
                        //if (getPptxPages(filePath) > maxPages) {
                            return otherFilePreview.notSupportedFile(model, fileAttribute, "页数大于" + maxPages + "页的文件请下载查看");
                       // }
                        //break;
                    case "doc":
                       // if (getDocPages(filePath) > maxPages) {
                            return otherFilePreview.notSupportedFile(model, fileAttribute, "页数大于" + maxPages + "页的文件请下载查看");
                       // }
                       // break;
                    case "docx":
                       // if (getDocxPages(filePath) > maxPages) {
                            return otherFilePreview.notSupportedFile(model, fileAttribute, "页数大于" + maxPages + "页的文件请下载查看");
                       // }
                       // break;
                }**/
                if (StringUtils.hasText(outFilePath)) {
                    logger.info("start office to pdf : " + fileName + " : " + DateUtil.now());
                    if("doc".equals(suffix) ||"docx".equals(suffix)) {
                        logger.info("word to pdf use aspose ");
                        XWFileOprUtil.registLic();
                        Document primaryDoc = new Document(filePath);
                        FontSettings fontSettings = primaryDoc.getFontSettings();
                        if(ObjectUtil.isEmpty(fontSettings)) {
                            fontSettings = new FontSettings();
                        }
                        String folder = fountPath;
                        logger.info("Set font folder:{}",folder);
                        fontSettings.setFontsFolder(folder,true);
                        primaryDoc.setFontSettings(fontSettings);
                        PdfSaveOptions options = new PdfSaveOptions();
                        options.getOutlineOptions().setHeadingsOutlineLevels(9);
                        options.getOutlineOptions().setExpandedOutlineLevels(9);
                        primaryDoc.save(outFilePath, options);
                    }else {
                        // 其他的格式转换成pdf
                        officeToPdfService.openOfficeToPDF(filePath, outFilePath);
                    }
                    logger.info("end office to pdf : " + fileName + " : " + DateUtil.now());
                    if (isHtml) {
                        // 对转换后的文件进行操作(改变编码方式)
                        fileHandlerService.doActionConvertedFile(outFilePath);
                        logger.info("convert end : " + fileName + " : " + DateUtil.now());
                    }
                    //如果不是html，则给pdf加水印
                    else {
                        pdfName = fileName.substring(0, fileName.lastIndexOf(".")) + "_wm.pdf";
                        String wateredOutFilePath = FILE_DIR + pdfName;
                        addWaterMark(outFilePath, wateredOutFilePath, fileAttribute.getWatermark());
                    }

                    if (ConfigConstants.isCacheEnabled()) {
                        // 加入缓存
                        fileHandlerService.addConvertedFile(pdfName, fileHandlerService.getRelativePath(outFilePath));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                return otherFilePreview.notSupportedFile(model, fileAttribute, "转换失败，请稍后重试：" + e.getLocalizedMessage());
            } finally {
                inProcess.remove(fileName);
            }
        }
        model.addAttribute("pdfUrl", StringPathUtils.pathUtil(cacheFileName));
        return isHtml ? EXEL_FILE_PREVIEW_PAGE : PDF_FILE_PREVIEW_PAGE;

    }

    static String getPreviewType(Model model, FileAttribute fileAttribute, String officePreviewType, String baseUrl, String pdfName, String outFilePath, FileHandlerService fileHandlerService, String officePreviewTypeImage, OtherFilePreviewImpl otherFilePreview) {
        String suffix = fileAttribute.getSuffix();
        boolean isPPT = suffix.equalsIgnoreCase("ppt") || suffix.equalsIgnoreCase("pptx");
        List<String> imageUrls = fileHandlerService.pdf2jpg(outFilePath, pdfName, baseUrl);
        if (imageUrls == null || imageUrls.size() < 1) {
            return otherFilePreview.notSupportedFile(model, fileAttribute, "office转图片异常，请联系管理员");
        }

        model.addAttribute("imgurls", StringPathUtils.pathUtils(imageUrls));
        model.addAttribute("currentUrl", StringPathUtils.pathUtil(imageUrls.get(0)));
        if (officePreviewTypeImage.equals(officePreviewType)) {
            // PPT 图片模式使用专用预览页面
            return (isPPT ? PPT_FILE_PREVIEW_PAGE : OFFICE_PICTURE_FILE_PREVIEW_PAGE);
        } else {
            return PICTURE_FILE_PREVIEW_PAGE;
        }

    }

    public static int getDocxPages(String docxPath) {
        try {
            XWPFDocument docx = new XWPFDocument(POIXMLDocument.openPackage(docxPath));
            return docx.getProperties().getExtendedProperties().getUnderlyingProperties().getPages();// 总页数
        } catch (Exception e) {
        }
        return 999;
    }

    public static int getDocPages(String docPath) {
        try {
            WordExtractor doc = new WordExtractor(new FileInputStream(docPath));
            return doc.getSummaryInformation().getPageCount();// 总页数
        } catch (Exception e) {
        }
        return 999;
    }

    public static int getPptPages(String pptPath) {
        try {
            FileInputStream fis = new FileInputStream(pptPath);
            SlideShow pptfile = new SlideShow(new HSLFSlideShow(fis));
            return pptfile.getSlides().length;
        } catch (Exception e) {
        }
        return 999;
    }

    public static int getPptxPages(String pptxPath) {
        try {
            XSLFSlideShow fis = new XSLFSlideShow(pptxPath);
            XMLSlideShow pptxfile = new XMLSlideShow(fis);
            return pptxfile.getSlides().length;
        } catch (Exception e) {
        }
        return 999;
    }

    public static void addWaterMark(String inputFile, String outputFile, String waterMarkName) throws Exception {
        PdfReader reader = null;
        PdfStamper stamper = null;
        try {
            reader = new PdfReader(inputFile);
            stamper = new PdfStamper(reader, new FileOutputStream(outputFile));

            BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);

            Rectangle pageRect;
            PdfGState gs = new PdfGState();
            gs.setFillOpacity(Float.parseFloat(WatermarkConfigConstants.getWatermarkAlpha()));
            gs.setStrokeOpacity(Float.parseFloat(WatermarkConfigConstants.getWatermarkAlpha()));
            int total = reader.getNumberOfPages() + 1;

            int textH = Integer.parseInt(WatermarkConfigConstants.getWatermarkHeight());
            int textW = Integer.parseInt(WatermarkConfigConstants.getWatermarkWidth());

            PdfContentByte under;
            for (int i = 1; i < total; i++) {
                pageRect = reader.getPageSizeWithRotation(i);
                under = stamper.getOverContent(i);
                under.saveState();
                under.setGState(gs);
                under.beginText();
                under.setFontAndSize(base, Integer.parseInt(WatermarkConfigConstants.getWatermarkFontsize().replace("px", "")));

                int watermarkHeight = Integer.parseInt(WatermarkConfigConstants.getWatermarkYSpace());
                int watermarkWidth = Integer.parseInt(WatermarkConfigConstants.getWatermarkXSpace());
                int watermarkAngel = Integer.parseInt(WatermarkConfigConstants.getWatermarkAngle());
                for (int height = 10; height < pageRect.getHeight() + textH; height = height + textH + watermarkHeight) {
                    for (int width = 20; width < pageRect.getWidth() + textW; width = width + textW + watermarkWidth) {
                        under.showTextAligned(Element.ALIGN_LEFT, waterMarkName, width, height, watermarkAngel);
                    }
                }
                // 添加水印文字
                under.endText();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (stamper != null) {
                stamper.close();
            }
            if (reader != null) {
                reader.close();
            }
        }
    }

}
