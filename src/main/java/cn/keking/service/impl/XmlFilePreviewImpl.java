package cn.keking.service.impl;

import cn.keking.model.FileAttribute;
import cn.keking.service.FilePreview;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

/**
 * <AUTHOR> (http://kailing.pub)
 * @since 2020/12/25
 */
@Service
public class XmlFilePreviewImpl implements FilePreview {

    private final SimTextFilePreviewImpl simTextFilePreview;
    private final OtherFilePreviewImpl otherFilePreview;

    public XmlFilePreviewImpl(SimTextFilePreviewImpl simTextFilePreview, OtherFilePreviewImpl otherFilePreview) {
        this.simTextFilePreview = simTextFilePreview;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        return otherFilePreview.notSupportedFile(model, fileAttribute, "暂不支持预览");
//        simTextFilePreview.filePreviewHandle(url, model, fileAttribute);
//        return XML_FILE_PREVIEW_PAGE;
    }
}
