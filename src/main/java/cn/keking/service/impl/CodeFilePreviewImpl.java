package cn.keking.service.impl;

import cn.keking.model.FileAttribute;
import cn.keking.service.FilePreview;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;

import java.util.List;

/**
 * <AUTHOR> (http://kailing.pub)
 * @since 2021/2/18
 */
@Component
public class CodeFilePreviewImpl implements FilePreview {

    private final SimTextFilePreviewImpl filePreviewHandle;
    private final OtherFilePreviewImpl otherFilePreview;

    public CodeFilePreviewImpl(SimTextFilePreviewImpl filePreviewHandle, OtherFilePreviewImpl otherFilePreview) {
        this.filePreviewHandle = filePreviewHandle;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        return otherFilePreview.notSupportedFile(model, fileAttribute, "暂不支持预览");
//         filePreviewHandle.filePreviewHandle(url, model, fileAttribute);
//        return CODE_FILE_PREVIEW_PAGE;
    }
}
