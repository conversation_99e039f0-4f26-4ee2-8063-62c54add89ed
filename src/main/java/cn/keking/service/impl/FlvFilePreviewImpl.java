package cn.keking.service.impl;

import cn.keking.model.FileAttribute;
import cn.keking.service.FilePreview;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

/**
 * <AUTHOR> kl
 * create : 2020-12-27 2:50 下午
 * flv文件预览处理实现
 **/
@Service
public class FlvFilePreviewImpl implements FilePreview {

    private final MediaFilePreviewImpl mediaFilePreview;
    private final OtherFilePreviewImpl otherFilePreview;

    public FlvFilePreviewImpl(MediaFilePreviewImpl mediaFilePreview, OtherFilePreviewImpl otherFilePreview) {
        this.mediaFilePreview = mediaFilePreview;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        return otherFilePreview.notSupportedFile(model, fileAttribute, "暂不支持预览");
//        mediaFilePreview.filePreviewHandle(url, model, fileAttribute);
//        return FLV_FILE_PREVIEW_PAGE;
    }
}
