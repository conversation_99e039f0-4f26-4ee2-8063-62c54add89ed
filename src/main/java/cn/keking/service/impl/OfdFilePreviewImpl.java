package cn.keking.service.impl;

import cn.keking.model.FileAttribute;
import cn.keking.service.FilePreview;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

/**
 * ofd 图片文件处理
 *
 * <AUTHOR> (http://kailing.pub)
 * @since 2021/2/8
 */
@Service
public class OfdFilePreviewImpl implements FilePreview {

    private final PictureFilePreviewImpl pictureFilePreview;
    private final OtherFilePreviewImpl otherFilePreview;

    public OfdFilePreviewImpl(PictureFilePreviewImpl pictureFilePreview, OtherFilePreviewImpl otherFilePreview) {
        this.pictureFilePreview = pictureFilePreview;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        return otherFilePreview.notSupportedFile(model, fileAttribute, "暂不支持预览");
//        pictureFilePreview.filePreviewHandle(url, model, fileAttribute);
//        return OFD_FILE_PREVIEW_PAGE;
    }
}
