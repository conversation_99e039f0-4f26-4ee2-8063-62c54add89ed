package cn.keking.service.impl;

import cn.keking.config.ConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import cn.keking.service.FilePreview;
import cn.keking.utils.DownloadUtils;
import cn.keking.service.FileHandlerService;
import cn.keking.utils.StringPathUtils;
import cn.keking.web.filter.BaseUrlFilter;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static cn.keking.service.impl.OfficeFilePreviewImpl.getPreviewType;

/**
 * <AUTHOR>
 * @since 2019/11/21 14:28
 */
@Service
public class CadFilePreviewImpl implements FilePreview {

    private static final String OFFICE_PREVIEW_TYPE_IMAGE = "image";
    private static final String OFFICE_PREVIEW_TYPE_ALL_IMAGES = "allImages";
    private static final String FILE_DIR = ConfigConstants.getFileDir();

    private final FileHandlerService fileHandlerService;
    private final OtherFilePreviewImpl otherFilePreview;

    public CadFilePreviewImpl(FileHandlerService fileHandlerService, OtherFilePreviewImpl otherFilePreview) {
        this.fileHandlerService = fileHandlerService;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
        return otherFilePreview.notSupportedFile(model, fileAttribute, "暂不支持预览");
//        // 预览Type，参数传了就取参数的，没传取系统默认
//        String officePreviewType = fileAttribute.getOfficePreviewType() == null ? ConfigConstants.getOfficePreviewType() : fileAttribute.getOfficePreviewType();
//        String baseUrl = BaseUrlFilter.getBaseUrl();
//        String fileName = fileAttribute.getName();
//        String suffix = fileAttribute.getSuffix();
//        String pdfName = fileName.substring(0, fileName.lastIndexOf(".") + 1) + "pdf";
//        String outFilePath = FILE_DIR + pdfName;
//        List<String> returnPath = new ArrayList<>();
//        // 判断之前是否已转换过，如果转换过，直接返回，否则执行转换
//        if (!fileHandlerService.listConvertedFiles().containsKey(pdfName) || !ConfigConstants.isCacheEnabled()) {
//            String filePath;
//            ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, null);
//            if (response.isFailure()) {
//                return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
//            }
//            filePath = response.getContent();
//            if (StringUtils.hasText(outFilePath)) {
//                boolean convertResult = fileHandlerService.cadToPdf(filePath, outFilePath);
//                if (!convertResult) {
//                    return otherFilePreview.notSupportedFile(model, fileAttribute, "cad文件转换异常，请联系管理员");
//                }
//                if (ConfigConstants.isCacheEnabled()) {
//                    // 加入缓存
//                    fileHandlerService.addConvertedFile(pdfName, fileHandlerService.getRelativePath(outFilePath));
//                }
//            }
//        }
//        if (baseUrl != null && (OFFICE_PREVIEW_TYPE_IMAGE.equals(officePreviewType) || OFFICE_PREVIEW_TYPE_ALL_IMAGES.equals(officePreviewType))) {
//            return getPreviewType(model, fileAttribute, officePreviewType, baseUrl, pdfName, outFilePath, fileHandlerService, OFFICE_PREVIEW_TYPE_IMAGE, otherFilePreview);
//        }
//
//        model.addAttribute("pdfUrl", StringPathUtils.pathUtil(pdfName));
//        return PDF_FILE_PREVIEW_PAGE;

    }


}
