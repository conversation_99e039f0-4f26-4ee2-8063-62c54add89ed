package cn.keking.service.impl;

import cn.keking.config.ConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import cn.keking.service.FileHandlerService;
import cn.keking.service.FilePreview;
import cn.keking.utils.DownloadUtils;
import cn.keking.utils.StringPathUtils;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by kl on 2018/1/17.
 * Content :图片文件处理
 */
@Service
public class PictureFilePreviewImpl implements FilePreview {

    private final FileHandlerService fileHandlerService;
    private final OtherFilePreviewImpl otherFilePreview;

    public PictureFilePreviewImpl(FileHandlerService fileHandlerService, OtherFilePreviewImpl otherFilePreview) {
        this.fileHandlerService = fileHandlerService;
        this.otherFilePreview = otherFilePreview;
    }

    @Override
    public String filePreviewHandle(String url, Model model, FileAttribute fileAttribute) {
//        List<String> imgUrls = new ArrayList<>();
//        imgUrls.add(url);
//        String fileKey = fileAttribute.getFileKey();
//        List<String> zipImgUrls = fileHandlerService.getImgCache(fileKey);
//        if (!CollectionUtils.isEmpty(zipImgUrls)) {
//            imgUrls.addAll(zipImgUrls);
//        }
//        // 不是http开头，浏览器不能直接访问，需下载到本地
//        if (url != null && !url.toLowerCase().startsWith("http")) {
//            ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, null);
//            if (response.isFailure()) {
//                return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
//            } else {
//                String file = fileHandlerService.getRelativePath(response.getContent());
//                imgUrls.clear();
//                imgUrls.add(file);
//                model.addAttribute("imgUrls", StringPathUtils.pathUtils(imgUrls));
//                model.addAttribute("currentUrl", StringPathUtils.pathUtil(file));
//            }
//        } else {
//            model.addAttribute("imgUrls", StringPathUtils.pathUtils(imgUrls));
//            model.addAttribute("currentUrl", StringPathUtils.pathUtil(url));
//        }


        String fileName = fileAttribute.getName();
        List<String> imgUrls = new ArrayList<>();
        String file = url;
        //当文件不存在时，就去下载
        if (!fileHandlerService.listConvertedFiles().containsKey(fileName) || !ConfigConstants.isCacheEnabled()) {
            ReturnResponse<String> response = DownloadUtils.downLoad(fileAttribute, fileName);
            if (response.isFailure()) {
                return otherFilePreview.notSupportedFile(model, fileAttribute, response.getMsg());
            }
            String outFilePath = response.getContent();
            file = fileHandlerService.getRelativePath(outFilePath);
            if (ConfigConstants.isCacheEnabled()) {
                // 加入缓存
                fileHandlerService.addConvertedFile(fileName, file);
            }
        }
        imgUrls.add(fileName);
        model.addAttribute("imgUrls", StringPathUtils.pathUtils(imgUrls));
        model.addAttribute("currentUrl", StringPathUtils.pathUtil(fileName));

        return PICTURE_FILE_PREVIEW_PAGE;
    }
}
