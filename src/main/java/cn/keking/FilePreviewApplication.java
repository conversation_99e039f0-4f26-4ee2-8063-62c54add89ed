package cn.keking;

import cn.jwis.configration.ConfigCenterHelper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.HashMap;
import java.util.Map;

@SpringBootApplication
@EnableScheduling
@ComponentScan(value = {"cn.keking.*"})
public class FilePreviewApplication {
    public static final String SERVICE_NAME= "file-preview-server";
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(FilePreviewApplication.class);

        //不再使用appName.properties中填写配置中心的服务名

        //改成SERVICE_NAME配置
        Map<String, Object> defaultMap = new HashMap<>();
        ConfigCenterHelper.getConfig(defaultMap, SERVICE_NAME);// 调用sdk包内读取配置的方法
        springApplication.setDefaultProperties(defaultMap);// 将配置加载到启动项中
        springApplication.run(args);
    }
}
