package cn.keking.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.keking.config.ConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.ReturnResponse;
import io.mola.galimatias.GalimatiasParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.*;
import java.util.UUID;

import static cn.keking.utils.KkFileUtils.isFtpUrl;
import static cn.keking.utils.KkFileUtils.isHttpUrl;

/**
 * <AUTHOR>
 */
public class DownloadUtils {

    private final static Logger logger = LoggerFactory.getLogger(DownloadUtils.class);
    private static final String fileDir = ConfigConstants.getFileDir();
    private static final String URL_PARAM_FTP_USERNAME = "ftp.username";
    private static final String URL_PARAM_FTP_PASSWORD = "ftp.password";
    private static final String URL_PARAM_FTP_CONTROL_ENCODING = "ftp.control.encoding";

    /**
     * @param fileAttribute fileAttribute
     * @param fileName      文件名
     * @return 本地文件绝对路径
     */
    public static ReturnResponse<String> downLoad(FileAttribute fileAttribute, String fileName) {
        String urlStr = fileAttribute.getUrl();
        ReturnResponse<String> response = new ReturnResponse<>(0, "下载成功!!!", "");
        String realPath = DownloadUtils.getRelFilePath(fileName, fileAttribute);
        try {
            URL url = WebUtils.normalizedURL(urlStr);
            if (isHttpUrl(url)) {
                File realFile = new File(realPath);
//                FileUtils.copyURLToFile(url,realFile);
//                HttpUtil.downloadFile(urlStr,realFile);

                boolean downloadSuccess = false;
                // 根据URL类型选择下载方式
                if (isMinioUrl(urlStr)) {
                    downloadSuccess = downloadMinioFile(urlStr, realFile);
                } else {
                    // 普通URL使用HttpUtil.downloadFile
                    try {
                        long size = HttpUtil.downloadFile(urlStr, realFile);
                        downloadSuccess = size > 0;
                        logger.info("普通文件下载成功: {}, 文件大小: {} bytes", realPath, size);
                    } catch (Exception e) {
                        logger.error("普通文件下载失败: {}", e.getMessage(), e);
                        downloadSuccess = false;
                    }
                }
                if (!downloadSuccess) {
                    response.setCode(1);
                    response.setMsg("文件下载失败");
                    return response;
                }
            } else if (isFtpUrl(url)) {
                String ftpUsername = WebUtils.getUrlParameterReg(fileAttribute.getUrl(), URL_PARAM_FTP_USERNAME);
                String ftpPassword = WebUtils.getUrlParameterReg(fileAttribute.getUrl(), URL_PARAM_FTP_PASSWORD);
                String ftpControlEncoding = WebUtils.getUrlParameterReg(fileAttribute.getUrl(), URL_PARAM_FTP_CONTROL_ENCODING);
                FtpUtils.download(fileAttribute.getUrl(), realPath, ftpUsername, ftpPassword, ftpControlEncoding);
            } else {
                response.setCode(1);
                response.setMsg("url不能识别url" + urlStr);
            }
            response.setContent(realPath);
            response.setMsg(fileName);
            return response;
        } catch (IOException | GalimatiasParseException e) {
            logger.error("文件下载失败，url：{}", urlStr, e);
            response.setCode(1);
            response.setContent(null);
            if (e instanceof FileNotFoundException) {
                response.setMsg("文件不存在!!!");
            } else {
                response.setMsg(e.getMessage());
            }
            return response;
        }
    }


    /**
     * 获取真实文件绝对路径
     *
     * @param fileName 文件名
     * @return 文件路径
     */
    private static String getRelFilePath(String fileName, FileAttribute fileAttribute) {
        String type = fileAttribute.getSuffix();
        if (null == fileName) {
            UUID uuid = UUID.randomUUID();
            fileName = uuid + "." + type;
        } else { // 文件后缀不一致时，以type为准(针对simText【将类txt文件转为txt】)
            fileName = fileName.replace(fileName.substring(fileName.lastIndexOf(".") + 1), type);
        }
        String realPath = fileDir + fileName;
        File dirFile = new File(fileDir);
        if (!dirFile.exists() && !dirFile.mkdirs()) {
            logger.error("创建目录【{}】失败,可能是权限不够，请检查", fileDir);
        }
        return realPath;
    }

    // Minio URL识别关键字
    private static final String[] MINIO_INDICATORS = {
            "X-Amz-Algorithm", "X-Amz-Signature", "X-Amz-Credential",
            "X-Amz-Date", "X-Amz-SignedHeaders"
    };

    /**
     * 判断是否为Minio URL
     */
    private static boolean isMinioUrl(String urlStr) {
        if (urlStr == null || urlStr.isEmpty()) {
            return false;
        }

        // 检查URL中是否包含AWS签名参数
        for (String indicator : MINIO_INDICATORS) {
            if (urlStr.contains(indicator)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 专用的Minio文件下载方法
     */
    private static boolean downloadMinioFile(String urlStr, File realFile) {
        try {
            logger.info("检测到MinIO URL，使用专用下载方法: {}", maskUrl(urlStr));

            // 创建HttpRequest，设置必要的请求头
            HttpRequest request = HttpRequest.get(urlStr)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                    .header("Accept", "*/*")
                    .header("Accept-Encoding", "gzip, deflate, br")
                    .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                    .header("Connection", "keep-alive")
                    .header("Cache-Control", "no-cache")
                    .timeout(60000); // 60秒超时，适应大文件

            // 执行请求
            HttpResponse response = request.execute();

            // 检查响应状态
            if (response.getStatus() != 200) {
                logger.error("MinIO文件下载失败，HTTP状态码: {}, 响应头: {}",
                        response.getStatus(), response.headers());

                // 记录响应体用于调试（限制长度）
                String responseBody = response.body();
                if (responseBody != null && responseBody.length() > 500) {
                    responseBody = responseBody.substring(0, 500) + "...";
                }
                logger.error("响应体: {}", responseBody);
                return false;
            }

            // 检查Content-Type，确保是文件而不是错误页面
            String contentType = response.header("Content-Type");
            if (contentType != null && contentType.contains("text/html")) {
                logger.warn("响应可能是HTML错误页面，Content-Type: {}", contentType);
            }

            // 将响应内容写入文件
            response.writeBody(realFile);

            // 验证文件是否下载成功
            if (!realFile.exists() || realFile.length() == 0) {
                logger.error("文件下载后验证失败，文件不存在或大小为0");
                return false;
            }

            logger.info("MinIO文件下载成功: {}, 文件大小: {} bytes",
                    realFile.getAbsolutePath(), realFile.length());
            return true;

        } catch (Exception e) {
            logger.error("MinIO文件下载异常，URL: {}, 错误: {}", urlStr, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 对URL进行脱敏处理，隐藏敏感的签名信息
     */
    private static String maskUrl(String url) {
        if (url == null) return null;

        // 隐藏签名参数的值
        return url.replaceAll("(X-Amz-Signature=)[^&]*", "$1***")
                .replaceAll("(X-Amz-Credential=)[^&]*", "$1***");
    }

}
