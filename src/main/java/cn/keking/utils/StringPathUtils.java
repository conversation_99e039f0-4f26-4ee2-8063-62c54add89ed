package cn.keking.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Component
public class StringPathUtils {


    private static String serviceUrl;

    private static final String fileUrl = "preview/file/";

    public static final String baseUrl = "preview/";

    public static String getServiceUrl() {
        return serviceUrl;
    }

    @Value("${service.url}")
    public void setServiceUrl(String serviceUrl) {
        StringPathUtils.serviceUrl = serviceUrl;
    }


    public static List<String> pathUtils(List<String> paths) {
        List<String> results = new ArrayList<>();
        if (!CollectionUtils.isEmpty(paths)) {
            for (String path : paths) {
                if (!StringUtils.isEmpty(path)) {
                    //本地跑的结果的分隔符是\
                    //String[] strings = path.split("\\\\");
                    //docker上跑的分隔符是/
                    String[] strings = path.split("/");
                    String result = strings[strings.length - 1];

                    results.add(getServiceUrl() + fileUrl + result);
                }
            }
            return results;
        }
        return null;
    }

    public static String pathUtil(String path) {
        if (StringUtils.hasText(path)) {
            String[] strings = path.split("/");
            String result = strings[strings.length - 1];
            return getServiceUrl() + fileUrl + result;
        }
        return null;
    }

}
