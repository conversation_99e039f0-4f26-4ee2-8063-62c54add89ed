package cn.keking.web.filter;

import cn.keking.config.ConfigConstants;
import cn.keking.config.WatermarkConfigConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @since 2020/5/13 18:34
 */
public class AttributeSetFilter implements Filter {

    public static final Logger logger = LoggerFactory.getLogger(AttributeSetFilter.class);

    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain filterChain) throws IOException, ServletException {
        this.setWatermarkAttribute(request);
        this.setFileAttribute(request);
        filterChain.doFilter(request, response);
    }

    /**
     * 设置办公文具预览逻辑需要的属性
     *
     * @param request request
     */
    private void setFileAttribute(ServletRequest request) {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        request.setAttribute("pdfDownloadDisable", ConfigConstants.DEFAULT_PDF_DOWNLOAD_DISABLE);
        request.setAttribute("fileKey", httpRequest.getParameter("fileKey"));
        request.setAttribute("switchDisabled", ConfigConstants.DEFAULT_OFFICE_PREVIEW_SWITCH_DISABLED);
        request.setAttribute("fileUploadDisable", ConfigConstants.DEFAULT_FILE_UPLOAD_DISABLE);
    }

    /**
     * 设置水印属性
     *
     * @param request request
     */
    private void setWatermarkAttribute(ServletRequest request) throws UnsupportedEncodingException {
//        String watermarkTxt = request.getParameter("watermarkTxt");
//        request.setAttribute("watermarkTxt", watermarkTxt != null ? watermarkTxt : WatermarkConfigConstants.DEFAULT_WATERMARK_TXT);
        request.setAttribute("watermarkXSpace", WatermarkConfigConstants.getWatermarkXSpace());
        request.setAttribute("watermarkYSpace", WatermarkConfigConstants.getWatermarkYSpace());
        request.setAttribute("watermarkFont", WatermarkConfigConstants.getWatermarkFont());
        request.setAttribute("watermarkFontsize", WatermarkConfigConstants.getWatermarkFontsize());
        request.setAttribute("watermarkColor", WatermarkConfigConstants.getWatermarkColor());
        request.setAttribute("watermarkAlpha", WatermarkConfigConstants.getWatermarkAlpha());
        request.setAttribute("watermarkWidth", WatermarkConfigConstants.getWatermarkWidth());
        request.setAttribute("watermarkHeight", WatermarkConfigConstants.getWatermarkHeight());
        request.setAttribute("watermarkAngle", WatermarkConfigConstants.getWatermarkAngle());
    }

    @Override
    public void destroy() {

    }
}
