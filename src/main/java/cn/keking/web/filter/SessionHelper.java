package cn.keking.web.filter;

import cn.keking.model.User;

/**
 * 线程局部变量中存储当前线程请求的用户信息/token/app信息
 *
 * <AUTHOR>
 */

public class SessionHelper {

    /**
     * 用户信息
     */
    private static ThreadLocal<User> user = new ThreadLocal<User>() {
        @Override
        protected User initialValue() {
            return super.initialValue();
        }

    };

    public static void addCurrentUser(User userIn) {
        user.set(userIn);
    }

    public static User getCurrentUser() {
        return user.get();
    }

    public static void removeCurrentUser() {
        user.remove();
    }

    /**
     * token
     */
    private static ThreadLocal<String> token = new ThreadLocal<String>();

    public static String getToken() {
        return token.get();
    }

    public static void setToken(String tokenInput) {
        token.set(tokenInput);
    }

    public static void removeToken() {
        token.remove();
    }

    /**
     * access token
     */
    private static ThreadLocal<String> accessToken = new ThreadLocal<String>();

    public static String getAccessToken() {
        String accesstoken = accessToken.get();
        if (accesstoken != null && accesstoken.indexOf(",") != -1) {
            accesstoken = accesstoken.split(",")[0];
        }
        return accesstoken;
    }

    public static void setAccessToken(String tokenInput) {
        accessToken.set(tokenInput);
    }

    public static void removeAccessToken() {
        accessToken.remove();
    }

    /**
     * appId
     */
    private static ThreadLocal<String> appId = new ThreadLocal<String>();

    public static String getAppId() {
        String appIdTemp = appId.get();
        if (appIdTemp != null && (appIdTemp.indexOf(",") != -1)) {
            appIdTemp = appIdTemp.split(",")[0];
        }
        return appIdTemp;
    }

    public static void setAppId(String appIdInput) {
        appId.set(appIdInput);
    }

    public static void removeAppId() {
        appId.remove();
    }
}
