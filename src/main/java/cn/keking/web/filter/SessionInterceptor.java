package cn.keking.web.filter;

import cn.jwis.common.presentation.response.Result;
import cn.keking.model.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.models.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;

/**
 * 对所有的request请求进行拦截，获取用户信息
 *
 * @<NAME_EMAIL>
 */
@Configuration
@Component
public class SessionInterceptor implements HandlerInterceptor {
    public static final Logger logger = LoggerFactory.getLogger(SessionInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        logger.info("Session Interceptor---request.headers:" + JSON.toJSONString(request.getHeaderNames()));
//        logger.info("Session Interceptor---request.header.user:" + JSON.toJSONString(request.getHeader("user")));
        // 获取用户信息
        User currentUser = null;
        String result = request.getHeader("user") != null ? URLDecoder.decode(request.getHeader("user"), "UTF-8") : null;
        logger.info("Session Interceptor---User Info From Gateway:" + result);
        if (!StringUtils.isEmpty(result)) {
            currentUser = JSON.parseObject(result, User.class);
        }
        if (currentUser == null) {
            currentUser = new User();
        }

        SessionHelper.addCurrentUser(currentUser);
//        logger.info("Session Interceptor---User Convert to JSON :" + JSON.toJSONString(currentUser));
        // 获取token信息
        String token = request.getHeader("token");
        SessionHelper.setToken(token);
//        logger.info("Session Interceptor---token:" + token);
        // 获取accesstoken信息
        String accesstoken = request.getHeader("accesstoken");
        String access_token = request.getHeader("access_token");
        if (!StringUtils.isEmpty(accesstoken)) {
            SessionHelper.setAccessToken(accesstoken);
        } else if (!StringUtils.isEmpty(access_token)) {
            SessionHelper.setAccessToken(access_token);
        }
//        logger.info("Session Interceptor---accesstoken:" + SessionHelper.getAccessToken());
        // 获取appId信息
        String appId = request.getHeader("appName");
        if (appId == null || ("".equals(appId))) {
            appId = request.getHeader("appId");
        }
        SessionHelper.setAppId(appId);
//        logger.info("Session Interceptor---appId:" + appId);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        SessionHelper.removeAccessToken();
        SessionHelper.removeToken();
        SessionHelper.removeAppId();
        SessionHelper.removeCurrentUser();
        if (ex != null) {
            Result re = new Result();
            re.setCode(-1);
            re.setMsg(ex.getMessage());
            response.getOutputStream().flush();
            response.getOutputStream().print(JSONObject.toJSONString(re));
            response.getOutputStream().close();
        }
    }
}
