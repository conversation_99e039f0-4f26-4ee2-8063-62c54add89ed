package cn.keking.web.controller;

import cn.hutool.core.date.DateUtil;
import cn.jwis.common.presentation.response.Result;
import cn.keking.config.WatermarkConfigConstants;
import cn.keking.model.FileAttribute;
import cn.keking.model.User;
import cn.keking.service.FilePreview;
import cn.keking.service.FilePreviewFactory;

import cn.keking.service.cache.CacheService;
import cn.keking.service.impl.OtherFilePreviewImpl;
import cn.keking.service.FileHandlerService;
import cn.keking.web.filter.SessionHelper;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Controller
public class OnlinePreviewController {

    public static final String BASE64_DECODE_ERROR_MSG = "Base64解码失败，请检查你的 %s 是否采用 Base64 + urlEncode 双重编码了！";
    private final Logger logger = LoggerFactory.getLogger(OnlinePreviewController.class);

    private final FilePreviewFactory previewFactory;
    private final CacheService cacheService;
    private final FileHandlerService fileHandlerService;
    private final OtherFilePreviewImpl otherFilePreview;

    public OnlinePreviewController(FilePreviewFactory filePreviewFactory, FileHandlerService fileHandlerService, CacheService cacheService, OtherFilePreviewImpl otherFilePreview) {
        this.previewFactory = filePreviewFactory;
        this.fileHandlerService = fileHandlerService;
        this.cacheService = cacheService;
        this.otherFilePreview = otherFilePreview;
    }

//    public static void main(String[] args) throws UnsupportedEncodingException {
//        String url = "http://192.168.2.143:9000/mpm/1687963887771_1B100001%2520wafer%25E5%259F%25BA%25E6%259D%25BF.pdf";
//        System.out.println(URLDecoder.decode(url,"UTF-8"));
//        System.out.println(URLDecoder.decode("中文","UTF-8"));
//        System.out.println(URLDecoder.decode("asdaf","UTF-8"));
//        System.out.println(URLDecoder.decode("http://192.168.2.143:9000/mpm/1687963887771.pdf","UTF-8"));
//    }

    @RequestMapping(value = "/onlinePreview")
    public String onlinePreview(String url, Model model, HttpServletRequest req) throws UnsupportedEncodingException {
        logger.info("onlinePreview 预览文件原始url：{}", url);
        // 新增：处理编码的Minio URL
        String processedUrl = processEncodedUrl(url, req);

        logger.info("onlinePreview 预览文件处理后url：{}", processedUrl);
        FileAttribute fileAttribute = fileHandlerService.getFileAttribute(processedUrl, req);
        model.addAttribute("file", fileAttribute);
        FilePreview filePreview = previewFactory.get(fileAttribute);
        logger.info("预览文件url：{}，previewType：{}", fileAttribute.getUrl(), fileAttribute.getType());
        String watermark = req.getParameter("watermarkTxt");
        if (null == watermark) {
            User user = SessionHelper.getCurrentUser();
            if (null != user) {
                watermark = user.getName() + DateUtil.today();
            } else {
                watermark = WatermarkConfigConstants.getWatermarkTxt();
            }
            if (null == watermark) {
                watermark = WatermarkConfigConstants.DEFAULT_WATERMARK_TXT;
            }
        }
        fileAttribute.setWatermark(watermark);
        if (!fileAttribute.isPdfView()) {
            req.setAttribute("watermarkTxt", watermark);
        } else {
            req.setAttribute("watermarkTxt", "");
        }
        return filePreview.filePreviewHandle(processedUrl, model, fileAttribute);
    }

    /**
     * 处理编码的URL
     * @param originalUrl 原始URL参数
     * @param req HTTP请求对象
     * @return 处理后的URL
     */
    private String processEncodedUrl(String originalUrl, HttpServletRequest req) {
//        String encodedUrl = req.getParameter("encodedUrl");
        String encodedUrl = originalUrl;

        if (StringUtils.hasText(encodedUrl)) {
            try {
                // Base64解码还原原始Minio URL
                byte[] decodedBytes = Base64.getDecoder().decode(encodedUrl);
                String decodedUrl = new String(decodedBytes, StandardCharsets.UTF_8);
                logger.info("成功解码Minio URL，原始长度：{}，解码后长度：{}", encodedUrl.length(), decodedUrl.length());
                return decodedUrl;
            } catch (IllegalArgumentException e) {
                logger.error("Base64解码失败，使用原始URL参数。编码URL：{}", encodedUrl, e);
            } catch (Exception e) {
                logger.error("URL解码过程中发生异常", e);
            }
        }

        // 如果没有encodedUrl参数或解码失败，使用原始url参数
        return originalUrl;
    }

//    @RequestMapping(value = "/picturesPreview")
//    public String picturesPreview(String urls, Model model, HttpServletRequest req) throws UnsupportedEncodingException {
//        String fileUrls;
//        try {
//            fileUrls = new String(Base64.decodeBase64(urls));
//        } catch (Exception ex) {
//            String errorMsg = String.format(BASE64_DECODE_ERROR_MSG, "urls");
//            return otherFilePreview.notSupportedFile(model, errorMsg);
//        }
//        logger.info("预览文件url：{}，urls：{}", fileUrls, urls);
//        // 抽取文件并返回文件列表
//        String[] images = fileUrls.split("\\|");
//        List<String> imgUrls = Arrays.asList(images);
//        model.addAttribute("imgUrls", imgUrls);
//
//        String currentUrl = req.getParameter("currentUrl");
//        if (StringUtils.hasText(currentUrl)) {
//            String decodedCurrentUrl = new String(Base64.decodeBase64(currentUrl));
//            model.addAttribute("currentUrl", decodedCurrentUrl);
//        } else {
//            model.addAttribute("currentUrl", imgUrls.get(0));
//        }
//        return PICTURE_FILE_PREVIEW_PAGE;
//    }

//    /**
//     * 根据url获取文件内容
//     * 当pdfjs读取存在跨域问题的文件时将通过此接口读取
//     *
//     * @param urlPath  url
//     * @param response response
//     */
//    @RequestMapping(value = "/getCorsFile", method = RequestMethod.GET)
//    public void getCorsFile(String urlPath, HttpServletResponse response) {
//        logger.info("下载跨域pdf文件url：{}", urlPath);
//        try {
//            URL url = WebUtils.normalizedURL(urlPath);
//            byte[] bytes = NetUtil.downloadBytes(url.toString());
//            IOUtils.write(bytes, response.getOutputStream());
//        } catch (IOException | GalimatiasParseException e) {
//            logger.error("下载跨域pdf文件异常，url：{}", urlPath, e);
//        }
//    }
//
//    /**
//     * 通过api接口入队
//     *
//     * @param url 请编码后在入队
//     */
//    @RequestMapping("/addTask")
//    @ResponseBody
//    public String addQueueTask(String url) {
//        logger.info("添加转码队列url：{}", url);
//        cacheService.addQueueTask(url);
//        return "success";
//    }

}
