package cn.keking.web.controller;

import cn.keking.config.ConfigConstants;
import cn.keking.utils.KkFileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.*;
import java.net.URLEncoder;

/**
 * 页面跳转
 *
 * <AUTHOR>
 * @date 2017/12/27
 */
@Controller
public class IndexController {

    private final Logger logger = LoggerFactory.getLogger(IndexController.class);

    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public String go2Index() {
        return "index";
    }

    @RequestMapping(value = "/", method = RequestMethod.GET)
    public String root() {
        return "redirect:/index";
    }

    @RequestMapping(value = "/file/{filename}", method = RequestMethod.GET)
    public ResponseEntity<byte[]> file(@PathVariable String filename) throws IOException {
        //下载文件的路径(这里绝对路径)
        String filepath = ConfigConstants.getFileDir() + filename;
        File file = new File(filepath);

        // 文件不存在时的兼容性处理
        logger.info("IndexController filename1=" + filename);
        if(!file.exists()){
            // 尝试URL编码的文件名（向后兼容）
            String encodedFilename = encodeURIComponent(filename);
            logger.info("IndexController filename2=" + encodedFilename);
            file = new File(ConfigConstants.getFileDir() + encodedFilename);

            // 如果URL编码的文件名也不存在，记录警告但继续处理
            if (!file.exists()) {
                logger.warn("文件不存在: " + filename + " 和 " + encodedFilename);
            }
        }

        //创建字节输入流，这里不实用Buffer类
        InputStream in = new FileInputStream(file);
        //available:获取输入流所读取的文件的最大字节数
        byte[] body = new byte[in.available()];
        //把字节读取到数组中
        in.read(body);
        //设置请求头
        MultiValueMap<String, String> headers = new HttpHeaders();
        String suffix = KkFileUtils.suffixFromFileName(filename);
        if("html".equals(suffix)){
            headers.add("Accept-Ranges", "bytes");
            headers.add("Content-Type", "text/html;charset=utf-8");
        }else {
            headers.add("Content-Disposition", "attchement;filename=" + file.getName());
        }
        //设置响应状态
        HttpStatus statusCode = HttpStatus.OK;
        in.close();
        ResponseEntity<byte[]> entity = new ResponseEntity<byte[]>(body, headers, statusCode);
        return entity;//返回
    }

    public static String encodeURIComponent(String input) throws UnsupportedEncodingException {
        String encoded = URLEncoder.encode(input, "UTF-8")
                .replaceAll("\\+", "%20")
                .replaceAll("\\%21", "!")
                .replaceAll("\\%27", "'")
                .replaceAll("\\%28", "(")
                .replaceAll("\\%29", ")")
                .replaceAll("\\%7E", "~");
        return encoded;
    }

}
